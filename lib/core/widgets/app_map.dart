import 'package:base_app/core/extension/app_extension_context.dart';
import 'package:base_app/core/theme/app_colors.dart';
import 'package:base_app/core/utils/safe_navigation_util.dart';
import 'package:base_app/core/widgets/app_animated_button.dart';
import 'package:base_app/features/home/<USER>/models/place_detail_response.dart';
import 'package:base_app/gen/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class AppMapbox extends StatefulWidget {
  final PlaceDetailResponse? placeCurrentDetail;
  final PlaceDetailResponse? placeDestinationDetail;
  const AppMapbox({
    super.key,
    this.placeCurrentDetail,
    this.placeDestinationDetail,
  });

  @override
  State<AppMapbox> createState() => _AppMapboxState();
}

class _AppMapboxState extends State<AppMapbox>
    with AutomaticKeepAliveClientMixin {
  MapboxMap? mapboxMap;
  PointAnnotationManager? pointAnnotationManager;
  PolylineAnnotationManager? polylineAnnotationManager;

  final buggyModelPosition = Position(106.6297, 10.8231);
  final carModelPosition = Position(106.6297, 10.8231);

  @override
  bool get wantKeepAlive => true;

  Future<void> _onMapCreated(MapboxMap map) async {
    debugPrint('=== onMapCreated called ===');
    mapboxMap = map;

    // Create annotation managers
    pointAnnotationManager =
        await mapboxMap?.annotations.createPointAnnotationManager();
    polylineAnnotationManager =
        await mapboxMap?.annotations.createPolylineAnnotationManager();

    await _addLocationMarkers();
    await _drawRouteLine();

    try {
      Future.wait([
        mapboxMap!.logo.updateSettings(LogoSettings(enabled: false)),
        mapboxMap!.attribution
            .updateSettings(AttributionSettings(enabled: false)),
        mapboxMap!.scaleBar.updateSettings(ScaleBarSettings(enabled: false)),
        mapboxMap!.compass.updateSettings(CompassSettings(enabled: false)),
      ]);
    } catch (e, stackTrace) {
      debugPrint('Error: $e');
      debugPrint('Stack: $stackTrace');
    }
  }

  Future<void> _addLocationMarkers() async {
    if (pointAnnotationManager == null) return;


    // Add current location marker
    if (widget.placeCurrentDetail?.lat != null &&
        widget.placeCurrentDetail?.lng != null) {
      final currentLocationBytes =
          await rootBundle.load(Assets.icons.iconCurrentLocation.path);
      final currentLocationImageData =
          currentLocationBytes.buffer.asUint8List();

      PointAnnotationOptions currentLocationOptions = PointAnnotationOptions(
        geometry: Point(
          coordinates: Position(
            widget.placeCurrentDetail!.lng!,
            widget.placeCurrentDetail!.lat!,
          ),
        ),
        image: currentLocationImageData,
        iconSize: 3.0,
      );

      pointAnnotationManager?.create(currentLocationOptions);
    }

    // Add destination marker
    if (widget.placeDestinationDetail?.lat != null &&
        widget.placeDestinationDetail?.lng != null) {
      final destinationBytes =
          await rootBundle.load(Assets.icons.iconDestinationLocation.path);
      final destinationImageData = destinationBytes.buffer.asUint8List();

      PointAnnotationOptions destinationOptions = PointAnnotationOptions(
        geometry: Point(
          coordinates: Position(
            widget.placeDestinationDetail!.lng!,
            widget.placeDestinationDetail!.lat!,
          ),
        ),
        image: destinationImageData,
        iconSize: 3.0,
      );

      pointAnnotationManager?.create(destinationOptions);
    }
  }

  Future<void> _drawRouteLine() async {
    if (polylineAnnotationManager == null) return;

    // Check if both locations are available
    if (widget.placeCurrentDetail?.lat == null ||
        widget.placeCurrentDetail?.lng == null ||
        widget.placeDestinationDetail?.lat == null ||
        widget.placeDestinationDetail?.lng == null) {
      return;
    }

    // Create line coordinates
    List<Position> lineCoordinates = [
      Position(
        widget.placeCurrentDetail!.lng!,
        widget.placeCurrentDetail!.lat!,
      ),
      Position(
        widget.placeDestinationDetail!.lng!,
        widget.placeDestinationDetail!.lat!,
      ),
    ];

    // Create polyline options
    PolylineAnnotationOptions polylineOptions = PolylineAnnotationOptions(
      geometry: LineString(coordinates: lineCoordinates),
      lineColor: Colors.blue.toARGB32(),
      lineWidth: 4.0,
      lineOpacity: 0.8,
    );

    // Add the line to the map
    polylineAnnotationManager?.create(polylineOptions);
  }

  Position _calculateCenterPosition() {
    // If both locations are available, calculate center point
    if (widget.placeCurrentDetail?.lat != null &&
        widget.placeCurrentDetail?.lng != null &&
        widget.placeDestinationDetail?.lat != null &&
        widget.placeDestinationDetail?.lng != null) {
      double centerLat = (widget.placeCurrentDetail!.lat! +
              widget.placeDestinationDetail!.lat!) /
          2;
      double centerLng = (widget.placeCurrentDetail!.lng! +
              widget.placeDestinationDetail!.lng!) /
          2;

      return Position(centerLng, centerLat);
    }

    // If only current location is available
    if (widget.placeCurrentDetail?.lat != null &&
        widget.placeCurrentDetail?.lng != null) {
      return Position(
          widget.placeCurrentDetail!.lng!, widget.placeCurrentDetail!.lat!);
    }

    // If only destination is available
    if (widget.placeDestinationDetail?.lat != null &&
        widget.placeDestinationDetail?.lng != null) {
      return Position(widget.placeDestinationDetail!.lng!,
          widget.placeDestinationDetail!.lat!);
    }

    // Default position (Ho Chi Minh City)
    return Position(106.6297, 10.8231);
  }

  double _calculateZoomLevel() {
    // If both locations are available, calculate appropriate zoom level
    if (widget.placeCurrentDetail?.lat != null &&
        widget.placeCurrentDetail?.lng != null &&
        widget.placeDestinationDetail?.lat != null &&
        widget.placeDestinationDetail?.lng != null) {
      // Calculate distance between points
      double latDiff = (widget.placeCurrentDetail!.lat! -
              widget.placeDestinationDetail!.lat!)
          .abs();
      double lngDiff = (widget.placeCurrentDetail!.lng! -
              widget.placeDestinationDetail!.lng!)
          .abs();

      double maxDiff = latDiff > lngDiff ? latDiff : lngDiff;

      // Adjust zoom level based on distance
      if (maxDiff > 0.1) return 10; // Very far apart
      if (maxDiff > 0.05) return 12; // Far apart
      if (maxDiff > 0.01) return 14; // Medium distance
      if (maxDiff > 0.005) return 15; // Close
      return 16; // Very close
    }

    // Default zoom for single location
    return 17;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        MapWidget(
          key: const ValueKey<String>('mapWidget'),
          mapOptions: MapOptions(
            pixelRatio: MediaQuery.of(context).devicePixelRatio,
          ),
          cameraOptions: CameraOptions(
            center: Point(coordinates: _calculateCenterPosition()),
            zoom: _calculateZoomLevel(),
            bearing: 0,
            pitch: 0,
          ),
          onMapCreated: _onMapCreated,
          onTapListener: _onMapTap,
        ),
        Positioned(
          top: MediaQuery.of(context).padding.top + 16,
          right: 16,
          child: AppAnimatedButton(
            onTap: () {
              SafeNavigationUtil.pop(context);
            },
            child: Container(
              padding: context.paddingNormal,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: context.borderRadiusMedium,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onMapTap(MapContentGestureContext context) {
    final coordinate = context.point;
    debugPrint(
        'Map tapped at: ${coordinate.coordinates.lat}, ${coordinate.coordinates.lng}');
  }
}
